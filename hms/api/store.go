//go:generate mockgen -destination=../mocks/store.go -package=mocks github.com/awa/go-iap/HMS IAP/api StoreAPIClient

package api

import (
	"bytes"
	"context"
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-confirm-purchase-for-sub-V5
const (
	HostProduction = "https://iap.cloud.huawei.com"

	// SubscriptionQuery 查询自动续期订阅商品的最新状态
	SubscriptionQuery = "/subscription/harmony/v1/application/subscription/status/query"
	// ConfirmOrder 订阅确认发货
	ConfirmOrder = "/subscription/harmony/v1/application/purchase/shipped/confirm"
)

type StoreConfig struct {
	KeyContent         []byte       // Loads a .p8 certificate
	KeyID              string       // Your private key ID from App Store Connect (Ex: 2X9R4HXF34)
	AID                string       // Your app’s app id
	Issuer             string       // Your issuer ID from the Keys page in App Store Connect (Ex: "57246542-96fe-1a63-e053-0824d011072a")
	Sandbox            bool         // default is Production
	TokenIssuedAtFunc  func() int64 // The token’s creation time func. Default is current timestamp.
	TokenExpiredAtFunc func() int64 // The token’s expiration time func. Default is one hour later.
}

type StoreClient struct {
	Token   *Token
	httpCli *http.Client
	cert    *Cert
}

// NewStoreClient create a HMS IAP server api client
func NewStoreClient(config *StoreConfig) *StoreClient {
	token := &Token{}
	token.WithConfig(config)

	client := &StoreClient{
		Token: token,
		cert:  &Cert{},
		httpCli: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
	return client
}

// NewStoreClientWithHTTPClient NewWithClient creates an App Store server api client with a custom http client.
func NewStoreClientWithHTTPClient(config *StoreConfig, httpClient *http.Client) *StoreClient {
	token := &Token{}
	token.WithConfig(config)

	client := &StoreClient{
		Token:   token,
		cert:    &Cert{},
		httpCli: httpClient,
	}
	return client
}

// GetSubscriptionQuery https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-query-subscription-status-V5#section9100101211811
func (a *StoreClient) GetSubscriptionQuery(ctx context.Context, body SubscriptionQueryRequest) (rsp *SubscriptionQueryResponse, err error) {
	baseURL := HostProduction + SubscriptionQuery

	URL := baseURL

	// Marshal the request body to JSON
	bodyBuf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	statusCode, rspBody, err := a.Do(ctx, http.MethodPost, URL, bodyBuf)
	if err != nil {
		return nil, err
	}

	if statusCode != http.StatusOK {
		return nil, fmt.Errorf("hms api: %v return status code %v", URL, statusCode)
	}

	if err = json.Unmarshal(rspBody, &rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

// ConfirmOrder https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-confirm-purchase-for-order-V5
func (a *StoreClient) ConfirmOrder(ctx context.Context, body ConfirmOrderRequest) (rsp *ConfirmOrderResponse, err error) {
	baseURL := HostProduction + ConfirmOrder
	URL := baseURL
	// Marshal the request body to JSON
	bodyBuf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	statusCode, rspBody, err := a.Do(ctx, http.MethodPost, URL, bodyBuf)
	if err != nil {
		return nil, err
	}

	if statusCode != http.StatusOK {
		return nil, fmt.Errorf("hms api: %v return status code %v", URL, statusCode)
	}

	if err = json.Unmarshal(rspBody, &rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

func (a *StoreClient) parseJWS(jwsEncode string, claims jwt.Claims) error {
	rootCertBytes, err := a.cert.extractCertByIndex(jwsEncode, 2)
	if err != nil {
		return err
	}
	rootCert, err := x509.ParseCertificate(rootCertBytes)
	if err != nil {
		return fmt.Errorf("HMS IAP failed to parse root certificate")
	}

	intermediaCertBytes, err := a.cert.extractCertByIndex(jwsEncode, 1)
	if err != nil {
		return err
	}
	intermediaCert, err := x509.ParseCertificate(intermediaCertBytes)
	if err != nil {
		return fmt.Errorf("HMS IAP failed to parse intermediate certificate")
	}

	leafCertBytes, err := a.cert.extractCertByIndex(jwsEncode, 0)
	if err != nil {
		return err
	}
	leafCert, err := x509.ParseCertificate(leafCertBytes)
	if err != nil {
		return fmt.Errorf("HMS IAP failed to parse leaf certificate")
	}
	if err = a.cert.verifyCert(rootCert, intermediaCert, leafCert); err != nil {
		return err
	}

	pk, ok := leafCert.PublicKey.(*ecdsa.PublicKey)
	if !ok {
		return fmt.Errorf("HMS IAP public key must be of type ecdsa.PublicKey")
	}

	_, err = jwt.ParseWithClaims(jwsEncode, claims, func(token *jwt.Token) (interface{}, error) {
		return pk, nil
	})
	return err
}

// Do Per doc: https://developer.apple.com/documentation/HMS IAPserverapi#topics
func (a *StoreClient) Do(ctx context.Context, method string, url string, body []byte) (int, []byte, error) {
	authToken, err := a.Token.GenerateIfExpired(body)
	if err != nil {
		return 0, nil, fmt.Errorf("HMS IAP generate token err %w", err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		return 0, nil, fmt.Errorf("HMS IAP new http request err %w", err)
	}

	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("Authorization", "Bearer "+authToken)
	req = req.WithContext(ctx)

	resp, err := a.httpCli.Do(req)
	if err != nil {
		return 0, nil, fmt.Errorf("HMS IAP http client do err %w", err)
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return resp.StatusCode, nil, fmt.Errorf("HMS IAP read http body err %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return resp.StatusCode, bodyBytes, fmt.Errorf("HMS IAP read http StatusCode err %w", err)
	}

	return resp.StatusCode, bodyBytes, err
}
