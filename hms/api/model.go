package api

type Environment string

// Environment https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-server-data-model-V5#section1950214943118
const (
	Sandbox    Environment = "SANDBOX"
	Production Environment = "NORMAL"
)

// ProductType https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-server-data-model-V5#section1950214943118
type ProductType string

const (
	Consumable    ProductType = "0"
	NonConsumable ProductType = "1"
	AutoRenewable ProductType = "2"
)

type FinishStatus string

const (
	FinishYes FinishStatus = "1"
	FinishNo  FinishStatus = "2"
)

type OfferType string

const (
	OfferDiscountTypePromotion           OfferType = "1"
	OfferDiscountTypeDiscountsPromotions OfferType = "2"
)

type ExpirationIntent string

const (
	ExpirationIntentUserCancellation  OfferType = "1"
	ExpirationIntentProductInvalid    OfferType = "2"
	ExpirationIntentInvalidSigning    OfferType = "3"
	ExpirationIntentAbnormalDeduction OfferType = "4"
	ExpirationIntentUserDoesPrice     OfferType = "5"
	ExpirationIntentUnknown           OfferType = "6"
)

type PurchaseOrderPayload struct {
	Environment                       Environment  `json:"environment"`
	PurchaseOrderID                   string       `json:"purchaseOrderId"`
	PurchaseToken                     string       `json:"purchaseToken"`
	ApplicationID                     string       `json:"applicationId"`
	ProductID                         string       `json:"productId"`
	ProductType                       ProductType  `json:"productType"`
	PurchaseTime                      int64        `json:"purchaseTime"`
	FinishStatus                      FinishStatus `json:"finishStatus"`
	NeedFinish                        bool         `json:"needFinish"`
	Price                             int64        `json:"price"`
	Currency                          string       `json:"currency"`
	DeveloperPayload                  string       `json:"developerPayload,omitempty"`
	PurchaseOrderRevocationReasonCode string       `json:"purchaseOrderRevocationReasonCode,omitempty"`
	RevocationTime                    int64        `json:"revocationTime,omitempty"`
	OfferTypeCode                     OfferType    `json:"offerTypeCode,omitempty"`
	OfferID                           string       `json:"offerId,omitempty"`
	CountryCode                       string       `json:"countryCode"`
	SignedTime                        int64        `json:"signedTime"`
	SubGroupGenerationID              string       `json:"subGroupGenerationId"`
	SubscriptionID                    string       `json:"subscriptionId,omitempty"`
	SubGroupID                        string       `json:"subGroupId,omitempty"`
	Duration                          string       `json:"duration,omitempty"`
	DurationTypeCode                  string       `json:"durationTypeCode,omitempty"`
}

type (
	SubscriptionQueryRequest struct {
		PurchaseOrderID string `json:"purchaseOrderId"`
		PurchaseToken   string `json:"purchaseToken"`
	}

	// SubscriptionQueryResponse https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-query-subscription-status-V5
	SubscriptionQueryResponse struct {
		ResponseCode      string `json:"responseCode"`
		ResponseMessage   string `json:"responseMessage"`
		JwsSubGroupStatus string `json:"jwsSubGroupStatus"`
	}
	SubGroupStatusPayload struct {
		Environment                   Environment          `json:"environment"`
		ApplicationID                 string               `json:"applicationId"`
		PackageName                   string               `json:"packageName"`
		SubGroupID                    string               `json:"subGroupId"`
		LastSubscriptionStatus        SubscriptionStatus   `json:"lastSubscriptionStatus"`
		HistorySubscriptionStatusList []SubscriptionStatus `json:"historySubscriptionStatusList"`
	}

	// ConfirmOrderRequest https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-confirm-purchase-for-sub-V5
	ConfirmOrderRequest struct {
		PurchaseOrderID string `json:"purchaseOrderId"`
		PurchaseToken   string `json:"purchaseToken"`
	}

	ConfirmOrderResponse struct {
		ResponseCode    string `json:"responseCode"`
		ResponseMessage string `json:"responseMessage"`
	}
)

type SubscriptionStatus struct {
	SubGroupGenerationID    string                 `json:"subGroupGenerationId"`
	SubscriptionID          string                 `json:"subscriptionId"`
	PurchaseToken           string                 `json:"purchaseToken"`
	Status                  string                 `json:"status"`
	ExpiresTime             int64                  `json:"expiresTime"`
	LastPurchaseOrder       PurchaseOrderPayload   `json:"lastPurchaseOrder"`
	RecentPurchaseOrderList []PurchaseOrderPayload `json:"recentPurchaseOrderList"`
	RenewalInfo             SubRenewalInfo         `json:"renewalInfo"`
}

type AutoRenewStatusCode string

const (
	AutoRenewOpen  AutoRenewStatusCode = "1"
	AutoRenewClose AutoRenewStatusCode = "0"
)

type HasInBillingRetryPeriod bool

type PriceIncreaseStatusCode string

const (
	PriceIncreaseStatusAgree   PriceIncreaseStatusCode = "2"
	PriceIncreaseStatusNoAgree PriceIncreaseStatusCode = "1"
)

type SubRenewalInfo struct {
	Environment              Environment             `json:"environment"`
	SubGroupGenerationID     string                  `json:"subGroupGenerationId"`
	NextRenewPeriodProductID string                  `json:"nextRenewPeriodProductId"`
	ProductID                string                  `json:"productId"`
	AutoRenewStatusCode      AutoRenewStatusCode     `json:"autoRenewStatusCode"`
	HasInBillingRetryPeriod  HasInBillingRetryPeriod `json:"hasInBillingRetryPeriod"`
	PriceIncreaseStatusCode  PriceIncreaseStatusCode `json:"priceIncreaseStatusCode,omitempty"`
	OfferTypeCode            OfferType               `json:"offerTypeCode,omitempty"`
	OfferID                  string                  `json:"offerId,omitempty"`
	RenewalPrice             int64                   `json:"renewalPrice,omitempty"`
	Currency                 string                  `json:"currency,omitempty"`
	RenewalTime              int64                   `json:"renewalTime,omitempty"`
	ExpirationIntent         ExpirationIntent        `json:"expirationIntent,omitempty"`
}

// JWSDecodedHeader https://developer.apple.com/documentation/appstoreserverapi/jwsdecodedheader
type JWSDecodedHeader struct {
	Alg string   `json:"alg,omitempty"`
	Kid string   `json:"kid,omitempty"`
	X5C []string `json:"x5c,omitempty"`
}
