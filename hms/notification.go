package hms

import "github.com/golang-jwt/jwt/v4"

// NotificationTypeV2 is type
type NotificationTypeV2 string

// list of notificationType
// https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-key-event-notifications-V5#section161113819282
const (
	// NotificationTypeDidChangeRenewalStatus 订阅状态发生改变
	NotificationTypeDidChangeRenewalStatus NotificationTypeV2 = "DID_CHANGE_RENEWAL_STATUS"
	// NotificationTypeRevoke 订单退款/撤销订阅。
	NotificationTypeRevoke NotificationTypeV2 = "REVOKE"
	// NotificationTypeDidNewTransaction 订单已购买/订阅已购买/订阅续订成功
	NotificationTypeDidNewTransaction NotificationTypeV2 = "DID_NEW_TRANSACTION"
	// NotificationTypeRenewalTimeModified 订阅过期时间调整。
	NotificationTypeRenewalTimeModified NotificationTypeV2 = "RENEWAL_TIME_MODIFIED"
	// NotificationTypeExpire 订阅已过期
	NotificationTypeExpire NotificationTypeV2 = "EXPIRE"
	// NotificationTypeSync 订单/订阅补发通知。在通知异常时，联系华为运营手动补发通知，此场景下无notificationSubtype。
	NotificationTypeSync NotificationTypeV2 = "SYNC"
)

// SubtypeV2 is type
type SubtypeV2 string

// list of subtypes
// https://developer.apple.com/documentation/appstoreservernotifications/subtype
const (
	// SubTypeInitialBuy  消耗型/非消耗型商品购买成功。自动续期订阅商品的第一次购买成功。
	SubTypeInitialBuy SubtypeV2 = "INITIAL_BUY"
	// SubTypeDidRenew 续期成功
	SubTypeDidRenew SubtypeV2 = "DID_RENEW"
	// SubTypeRestore 用户主动恢复了一个订阅型商品，续期恢复正常
	SubTypeRestore SubtypeV2 = "RESTORE"
	// SubTypeAutoRenewEnabled 自动续期功能开启
	SubTypeAutoRenewEnabled SubtypeV2 = "AUTO_RENEW_ENABLED"
	// SubTypeAutoRenewDisabled 自动续期功能关闭
	SubTypeAutoRenewDisabled SubtypeV2 = "AUTO_RENEW_DISABLED"
	// SubTypeDowngrade 用户调整自动续期订阅商品降级或跨级且在下个续订生效。
	SubTypeDowngrade SubtypeV2 = "DOWNGRADE"
	// SubTypeUpgrade 用户调整自动续期订阅商品升级或跨级且立即生效。
	SubTypeUpgrade SubtypeV2 = "UPGRADE"
	// SubTypeRefundTransaction 消耗型/非消耗型商品订单退款成功。自动续期订阅商品订单退款成功
	SubTypeRefundTransaction SubtypeV2 = "REFUND_TRANSACTION"
	// SubTypeBillingRetry 	一个到期的自动续期订阅商品进入账号保留期
	SubTypeBillingRetry SubtypeV2 = "BILLING_RETRY"
	// SubTypePriceIncrease 用户同意了涨价
	SubTypePriceIncrease SubtypeV2 = "PRICE_INCREASE"
	// SubTypeBillingRecovery 订阅重试扣费成功
	SubTypeBillingRecovery SubtypeV2 = "BILLING_RECOVERY"
	// SubTypeProductNotForSale 商品不存在。
	SubTypeProductNotForSale SubtypeV2 = "PRODUCT_NOT_FOR_SALE"
	// SubTypeApplicationDeleteSubscriptionHosting 撤销订阅成功，订阅权益会立即取消
	SubTypeApplicationDeleteSubscriptionHosting SubtypeV2 = "APPLICATION_DELETE_SUBSCRIPTION_HOSTING"
)

// ProductType https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-server-data-model-V5#section1950214943118
type ProductType int

const (
	Consumable ProductType = iota
	NonConsumable
	AutoRenewable
)

type (

	// JwsNotification is struct
	// https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-key-event-notifications-V5#section526717832913
	JwsNotification struct {
		JwsNotification string `json:"jwsNotification"`
	}

	// NotificationPayload is struct
	// https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-key-event-notifications-V5#section526717832913
	NotificationPayload struct {
		NotificationType      NotificationTypeV2   `json:"notificationType"`
		NotificationSubtype   SubtypeV2            `json:"notificationSubtype"`
		NotificationRequestID string               `json:"notificationRequestId"`
		NotificationMetaData  NotificationMetaData `json:"notificationMetaData"`
		NotificationVersion   string               `json:"notificationVersion"`
		SignedTime            int64                `json:"signedTime"`
		jwt.RegisteredClaims
	}

	// NotificationMetaData is struct
	// https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/iap-key-event-notifications-V5#section526717832913
	NotificationMetaData struct {
		Environment          Environment `json:"environment"`
		ApplicationID        string      `json:"applicationId"`
		PackageName          string      `json:"packageName"`
		Type                 ProductType `json:"type"`
		CurrentProductID     string      `json:"currentProductId"`
		SubGroupId           string      `json:"subGroupId"`
		SubGroupGenerationID string      `json:"subGroupGenerationId"`
		SubscriptionId       string      `json:"subscriptionId"`
		PurchaseToken        string      `json:"purchaseToken"`
		PurchaseOrderID      string      `json:"purchaseOrderID,omitempty"`
	}
	// SubscriptionNotificationV2JWSDecodedHeader is struct
	SubscriptionNotificationV2JWSDecodedHeader struct {
		Alg string   `json:"alg"`
		Kid string   `json:"kid"`
		X5c []string `json:"x5c"`
	}
)
