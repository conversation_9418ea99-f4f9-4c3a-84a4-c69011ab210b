package hms

import (
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// Client implements IAPClient
type Client struct {
	httpCli *http.Client
}

// New creates a client object
func New() *Client {
	client := &Client{
		httpCli: &http.Client{
			Timeout: 10 * time.Second,
			Transport: &http.Transport{
				DisableKeepAlives: true,
			},
		},
	}
	return client
}

// ParseNotificationV2 parse notification from App Store Server
func (c *Client) ParseNotificationV2(tokenStr string, result *jwt.Token) error {
	cert := Cert{}

	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		return cert.ExtractPublicKeyFromToken(tokenStr)
	})
	if token != nil {
		*result = *token
	}
	return err
}

// ParseNotificationV2WithClaim parse notification from App Store Server
func (c *Client) ParseNotificationV2WithClaim(tokenStr string, result jwt.Claims) error {
	cert := Cert{}
	_, err := jwt.ParseWithClaims(tokenStr, result, func(token *jwt.Token) (interface{}, error) {
		return cert.ExtractPublicKeyFromToken(tokenStr)
	})
	return err
}
