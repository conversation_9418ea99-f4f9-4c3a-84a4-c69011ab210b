package wechat

import (
	"testing"

	"gitlab.dailyyoga.com.cn/server/gopay/pkg/xlog"
)

var (
	publicPKCS1 = `
-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEA4//8F2TVtGTU18XAdbJ4O+S8D+YrtQOepDnAyRMli52NQPbf4e41
XprsIQYZ8qkbRjmLCTXI+Pz5g5AZZXVXQ284OY0OUyS5L28SlEXxTyFuv/jHtvt1
WvHOtMPXL6epyenvo2OAIEP7fAVQjyftWE9w+x1A01J5QOlWruc4M15ewkp5Dsyj
fjNF5MG51wSmcWsGCAIZ0POPNrvf/pYtaWq/4eK6GJAlJ+oytaaZBE0T+MpYoL2j
k6ranOYqPK7LVLMy3txRIJMtpjjb+Dc2SwV3tIeYKwuYu64gf6FiQjHwpSEFQ+CF
MdHYYEoxIgt8W1xB3SGInV6d5HZ9f/wLWwIDAQAB
-----END RSA PUBLIC KEY-----`

	privatePKCS1 = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

func TestV3EncryptTextAndV3DecryptText(t *testing.T) {
	text := "I love GoPay"
	cipherText, err := V3EncryptText(text, []byte(publicPKCS1))
	if err != nil {
		xlog.Errorf("V3EncryptText.Err:", err)
		return
	}
	xlog.Debugf("encrypt text: %s", cipherText)

	originText, err := V3DecryptText(cipherText, []byte(privatePKCS1))
	if err != nil {
		xlog.Errorf("V3DecryptText.Err:", err)
		return
	}
	xlog.Debugf("decrypt text: %s", originText)
}
