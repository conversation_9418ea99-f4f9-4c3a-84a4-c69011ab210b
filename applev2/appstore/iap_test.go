package appstore_test

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/golang-jwt/jwt/v4"
	"gitlab.dailyyoga.com.cn/server/gopay/applev2/appstore"
	"gitlab.dailyyoga.com.cn/server/gopay/applev2/appstore/api"
)

//func TestParseNotificationV3(t *testing.T) {
//	client := appstore.New()
//	tokenStr := `*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
//	token := jwt.Token{}
//
//	if err := client.ParseNotificationV2(tokenStr, &token); err != nil {
//		t.Errorf("expected an error to be nil since the tokenStr is legal")
//	}
//	claims, ok := token.Claims.(jwt.MapClaims)
//	if !ok {
//		t.<PERSON>rrorf("claim should be jwt.MapClaims")
//	}
//	aaa, _ := json.Marshal(claims)
//	t.Log(string(aaa))
//	t.Log("ok")
//}

//const ACCOUNTPRIVATEKEY = `
//-----BEGIN PRIVATE KEY-----
//MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgou/55mCTTW52Z6Em
//lnnxlEVcWdegxo30vmVF1j+ZC+2gCgYIKoZIzj0DAQehRANCAAQlUFh3iHCN5+NL
//SqzwvylghOagbpVKmAORA138DOgZ2G1HZieY6vh9aCKQYLL5jU9j6L6bX+f2canV
//pj7w8IE6
//-----END PRIVATE KEY-----
//`
//
//func TestRequestV2(t *testing.T) {
//	c := &api.StoreConfig{
//		KeyContent: []byte(ACCOUNTPRIVATEKEY),              // Loads a .p8 certificate
//		KeyID:      "57DK8VBC55",                           // Your private key ID from App Store Connect (Ex: 2X9R4HXF34)
//		BundleID:   "com.yinniu.stretch",                   // Your app's bundle ID
//		Issuer:     "e6f80131-f1dd-490b-b1ff-9cead8bd1dc3", // Your issuer ID from the Keys page in App Store Connect (Ex: "********-96fe-1a63-e053-0824d011072a")
//		Sandbox:    false,                                  // default is Production
//	}
//	transactionId := "***************"
//	a := api.NewStoreClient(c)
//	ctx := context.Background()
//	response, err := a.GetTransactionHistory(ctx, transactionId, nil)
//	if err != nil {
//		t.Error(err)
//	}
//	for _, v := range response {
//		transantion, err := a.ParseSignedTransactions(v.SignedTransactions)
//		if err != nil {
//			// error handling
//			continue
//		}
//		for _, tb := range transantion {
//			t.Logf("原始揭秘数据：%+v", tb)
//		}
//	}
//}

//func TestGetTransactionInfoV2(t *testing.T) {
//	c := &api.StoreConfig{
//		KeyContent: []byte(ACCOUNTPRIVATEKEY),              // Loads a .p8 certificate
//		KeyID:      "57DK8VBC55",                           // Your private key ID from App Store Connect (Ex: 2X9R4HXF34)
//		BundleID:   "com.yinniu.stretch",                   // Your app's bundle ID
//		Issuer:     "e6f80131-f1dd-490b-b1ff-9cead8bd1dc3", // Your issuer ID from the Keys page in App Store Connect (Ex: "********-96fe-1a63-e053-0824d011072a")
//		Sandbox:    false,                                  // default is Production
//	}
//	transactionId := "***************"
//	a := api.NewStoreClient(c)
//	ctx := context.Background()
//	response, err := a.GetTransactionInfo(ctx, transactionId)
//	if err != nil {
//		t.Error(err)
//	}
//	t.Logf("原始揭秘数据：%+v", response)
//
//	transantion, err := a.ParseSignedTransaction(response.SignedTransactionInfo)
//	t.Logf("原始揭秘数据：%+v", transantion)
//}

//
//func TestSubscriptionStatuses(t *testing.T) {
//	c := &api.StoreConfig{
//		KeyContent: []byte(ACCOUNTPRIVATEKEY),              // Loads a .p8 certificate
//		KeyID:      "57DK8VBC55",                           // Your private key ID from App Store Connect (Ex: 2X9R4HXF34)
//		BundleID:   "com.yinniu.stretch",                   // Your app's bundle ID
//		Issuer:     "e6f80131-f1dd-490b-b1ff-9cead8bd1dc3", // Your issuer ID from the Keys page in App Store Connect (Ex: "********-96fe-1a63-e053-0824d011072a")
//		Sandbox:    false,                                  // default is Production
//	}
//	transactionId := "**************"
//	a := api.NewStoreClient(c)
//	ctx := context.Background()
//	response, err := a.GetALLSubscriptionStatuses(ctx, transactionId, nil)
//	if err != nil {
//		t.Error(err)
//	}
//	t.Logf("原始揭秘数据：%+v", response)
//}

const AppStoreAccountPrivateKeyDY = `
******************************************************************************************************************************************************************************************************************************************************************`

// key_id: "98U4K555Q9"
// bundle_id: "yogadaily"
// issuer: "69a6de78-874c-47e3-e053-5b8c7c11a4d1"
func TestSubscriptionStatuses(t *testing.T) {
	c := &api.StoreConfig{
		KeyContent: []byte(AppStoreAccountPrivateKeyDY),    // Loads a .p8 certificate
		KeyID:      "98U4K555Q9",                           // Your private key ID from App Store Connect (Ex: 2X9R4HXF34)
		BundleID:   "yogadaily",                            // Your app's bundle ID
		Issuer:     "69a6de78-874c-47e3-e053-5b8c7c11a4d1", // Your issuer ID from the Keys page in App Store Connect (Ex: "********-96fe-1a63-e053-0824d011072a")
		Sandbox:    false,                                  // default is Production
	}
	a := api.NewStoreClient(c)
	ctx := context.Background()
	body := api.NotificationHistoryRequest{
		StartDate:        *************,
		EndDate:          *************,
		NotificationType: appstore.NotificationTypeV2("DID_RENEW"),
		//NotificationSubtype: appstore.SubtypeV2("RESUBSCRIBE"),
	}
	response, err := a.GetNotificationHistory(ctx, body, "")
	if err != nil {
		t.Error(err)
	}
	// t.Logf("原始揭秘数据：%+v", response)
	for _, v := range response.NotificationHistory {
		payload := &appstore.SubscriptionNotificationV2DecodedPayload{}
		err := a.ParseJWSWithClaims(v.SignedPayload, payload)
		if err != nil {
			t.Error(err)
			return
		}
		notificationsData := getNotificationsDataJwsDecoded(payload)
		renewalInfo, _ := json.Marshal(notificationsData.RenewalInfo)
		transaction, _ := json.Marshal(notificationsData.Transaction)
		t.Logf("Decoded RenewalInfo: %s\n Transaction:%s", string(renewalInfo), string(transaction))
		t.Logf("Decoded NotificationType: %+v\n Subtype:%+v", payload.NotificationType, payload.Subtype)
		t.Logf("Decoded NotificationUUID: %+v\n ", payload.NotificationUUID)
		t.Logf("Decoded payload: %+v\n ", payload.Data.Status)
		return
	}
}

type DataJwsDecodedPayload struct {
	RenewalInfo *appstore.JWSRenewalInfoDecodedPayload
	Transaction *appstore.JWSTransactionDecodedPayload
}

// getNotificationsDataJwsDecoded 获取回调关键信息
func getNotificationsDataJwsDecoded(n *appstore.SubscriptionNotificationV2DecodedPayload) *DataJwsDecodedPayload {
	resp := &DataJwsDecodedPayload{}
	renewalInfoDecodedPayload, err := getRenewalInfoDecodedPayload(n.Data.SignedRenewalInfo)
	if err == nil {
		resp.RenewalInfo = renewalInfoDecodedPayload
	}
	transactionDecodedPayload, err := getTransactionDecodedPayload(n.Data.SignedTransactionInfo)
	if err == nil {
		resp.Transaction = transactionDecodedPayload
	}
	return resp
}

func getRenewalInfoDecodedPayload(signedRenewalInfo appstore.JWSRenewalInfo) (*appstore.JWSRenewalInfoDecodedPayload,
	error) {
	claims, err := ParseNotificationData(string(signedRenewalInfo))
	if err != nil {
		return nil, err
	}
	notificationData, _ := json.Marshal(claims)
	var pResult appstore.JWSRenewalInfoDecodedPayload
	err = json.Unmarshal(notificationData, &pResult)
	if err != nil {
		return nil, err
	}
	return &pResult, nil
}
func ParseNotificationData(tokenStr string) (map[string]interface{}, error) {
	client := appstore.New()
	token := jwt.Token{}
	if err := client.ParseNotificationV2(tokenStr, &token); err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("解密失败")
	}
	return claims, nil
}

func getTransactionDecodedPayload(transaction appstore.JWSTransaction) (*appstore.JWSTransactionDecodedPayload, error) {
	claims, err := ParseNotificationData(string(transaction))
	if err != nil {
		return nil, err
	}
	notificationData, _ := json.Marshal(claims)
	var pResult appstore.JWSTransactionDecodedPayload
	err = json.Unmarshal(notificationData, &pResult)
	if err != nil {
		return nil, err
	}
	return &pResult, nil
}
