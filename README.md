# GoPay

### 微信、支付宝、PayPal、QQ 的 Golang 版本SDK

---
<br>

# 一、安装

```bash
go get -u gitlab.dailyyoga.com.cn/server/gopay
```

#### 查看 GoPay 版本

  [版本更新记录](https://gitlab.dailyyoga.com.cn/server/gopay/blob/main/release_note.txt)

```go
import (
    "gitlab.dailyyoga.com.cn/server/gopay"
    "gitlab.dailyyoga.com.cn/server/gopay/pkg/xlog"
)

func main() {
    xlog.Info("GoPay Version: ", gopay.Version)
}
```

---

<br>

<br>

# 二、其他说明

* 各支付方式接入，请仔细查看 `xxx_test.go` 使用方式
    * `gopay/wechat/v3/client_test.go`
    * `gopay/alipay/client_test.go`
    * `gopay/qq/client_test.go`
    * `gopay/paypal/client_test.go`
    * `gopay/apple/verify_test.go`
    * 或 examples
* 开发过程中，请尽量使用正式环境，1分钱测试法！
---
