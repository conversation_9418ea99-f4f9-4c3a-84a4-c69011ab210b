package alipay_test

import (
	"context"
	"testing"
	"gitlab.dailyyoga.com.cn/server/gopay"
	"gitlab.dailyyoga.com.cn/server/gopay/pkg/xlog"
	"gitlab.dailyyoga.com.cn/server/gopay/alipay"
)

func TestUserInfoAuth(t *testing.T) {
	//aliPayPublicKey := "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1wn1sU/8Q0rYLlZ6sq3enrPZw2ptp6FecHR2bBFLjJ+sKzepROd0bKddgj+Mr1ffr3Ej78mLdWV8IzLfpXUi945DkrQcOUWLY0MHhYVG2jSs/qzFfpzmtut2Cl2TozYpE84zom9ei06u2AXLMBkU6VpznZl+R4qIgnUfByt3Ix5b3h4Cl6gzXMAB1hJrrrCkq+WvWb3Fy0vmk/DUbJEz8i8mQPff2gsHBE1nMPvHVAMw1GMk9ImB4PxucVek4ZbUzVqxZXphaAgUXFK2FSFU+Q+q1SPvHbUsjtIyL+cLA6H/6ybFF9Ffp27Y14AHPw29+243/SpMisbGcj2KD+evBwIDAQAB"
	privateKey := "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDDvu4KO2tx0EyDtYvP7eajNP5uWNq/gy3q/MrIe72R76gE4X8rDu670M8vccVwDy5QMEPlgAtw3RQa2XZt91doDJG8EW0Mlceq6V9JHkuKsBPpqoMMWlCOBDP0cxkXjbuk4/lBT5Oo9NfZd1YHlLPca8AtYpSfevqM2+TIpUjYfunr9oHkuZXUTd6wsiJsKrAvXCiDznO8XkOT9WM+dIL7T1K7RkyIWpKBE9fHIxn77WCYDW70DrlrdkQjNcH2K2200hXdhAkMJNS93wkGEbbFcR28rdpyrkFkVI6NPegKKoJUD6xW2HGWaVwIQuj2Kwzl5jely7C14lXtKTEdfXSJAgMBAAECggEBAJh87pdN/r+lE1YvYYFTxS4zJFn2IkK7AFRlJdvUebNsTSmwOQ7kDRqG6j/9k19elCen0dzBSShdMUJRUi8MVi0aTZWCxb7eF7OPJcmiLhsWXAzV5k0DNmnL4GV4HH36zckAR7aXNWKTimtAyeYR86Os1QMGHt22QeTlkG62Q+9SYf0AjP/96DiEZ6a2w1PNea75mN4Zk0/tHge77FfRD4EvtM3tjDz/vmB0OvH30eq6hdfgK7Qa1MewGMyOLI6/ToyFpav0GJaC2yRxvg0oqUkd/D/Q7D+LtWn3jUeYmwx6qFV4BQiq62gNDFmjFjUIs4ocwZk572hmfa52rey38pkCgYEA6ReKCOGGW3TpZmOK58Wv7f6VENH6WXqneaYbtyjkhHncM6Qx/i+x34bis5f4isdGze7Qs6MGKjPqE/MyLBDGnGSsCtMtS2flvTga0GqRY4g0j4UeOq39h8E9ZjUTthFsI7+mRiZr8QOUTcDYABHRQwASdzIPENOFmVb4MRqQO/8CgYEA1vvIg+F6uxj6gcXV1jiaKSG/mhuoeJkkJA1FJfaMCUkAr2dL2oX34MbkhA5/0CXqnTL63bF0qczZVfZGRSa189enbl2Y79ToclCe2iIuh5mA1+sOmIkM7Fpy0A6QxkTkafOXY0cEvGjKQ1OqVNRFNQTnf+DHs/W1OoVhtP2Sb3cCgYBMvs1aAzdDoYi/h4LSsQot6CaYdSpij7nYmdKCSdqDq8PO1P8I3BdKq5S04RKaTuaHI1xf6Qs8lf86NlaMn7FlCNiEb+d37mt8E57whEX+aXg00PWVCHhzXDKlFlC5H59lXdKXQktD/ZO0zgvQag1ma/2odTj0p1BTnoj1UfOvbQKBgQCq5ij2Pc6U4Txy4VQmIYTbYcqwtX/trr++MRZhIWcdEn2K8nCoXIs6kqGiIOyWCS5CnP40MhdtzDyRS199JVLLbwnYgGtNbFEEXvaIrVeT0AFOo7tMotY1JYnTv2BzXNy/JTeZYiFvrVXsw5vKJ5yEk0t8qrHWa2MQDGwpttIr7QKBgDPiKriI+ZK0/XmB2lscGODBYqjh4BJYcny+OItcn3GXLpRyTKXov0UrlwJbUbJITB8G47HEz1XtY7EVhb6iE1oimo4dWYWv3wUfvzXVOxCRKsUvDXcOiw6l4cwsBbeZpXxKOaCUz+emRnkdJM2b73UDT12MQ1xjNeu4KbI++6N1"
	//初始化支付宝客户端
	//    appId：应用ID
	//    privateKey：应用秘钥
	//    isProd：是否是正式环境
	client, err := alipay.NewClient("2021004106663048", privateKey, false)
	if err != nil {
		xlog.Error(err)
		return
	}
	//配置公共参数
	client.SetCharset("utf-8").
		SetSignType("RSA2")

	// 请求参数
	bm := make(gopay.BodyMap)
	// 接口权限值，目前只支持auth_user和auth_base两个值。具体说明看文档介绍
	bm.Set("scopes", []string{"auth_base"})
	bm.Set("state", "init")

	// 发起请求
	aliRsp, err := client.UserInfoAuth(context.Background(), bm)
	if err != nil {
		xlog.Error("err:", err)
		return
	}
	xlog.Debug("aliRsp:", aliRsp)
	t.Logf("aliRsp:%+v", "https://openapi.alipay.com/gateway.do?"+aliRsp)
}
