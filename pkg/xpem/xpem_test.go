package xpem

import (
	"testing"

	"gitlab.dailyyoga.com.cn/server/gopay/pkg/xlog"
)

var (
	PrivateKeyContent = `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
)

func TestDecodePrivateKey(t *testing.T) {
	_, err := DecodePrivateKey([]byte(PrivateKeyContent))
	if err != nil {
		xlog.Error(err)
		return
	}
	xlog.Info("decode ok")
}
